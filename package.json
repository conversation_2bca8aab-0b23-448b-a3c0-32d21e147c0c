{"name": "kidventure-app", "version": "1.0.116", "private": true, "dependencies": {"@amcharts/amcharts5": "^5.12.1", "@amcharts/amcharts5-geodata": "^5.1.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/analytics": "^0.10.12", "@mui/material": "^7.0.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "date-fns": "^4.1.0", "firebase": "^11.6.0", "framer-motion": "^12.6.3", "html2canvas": "^1.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "recharts": "^2.15.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "npm run generate-version && react-scripts build", "build:store": "cross-env REACT_APP_IS_STORE_VERSION=true npm run generate-version && cross-env REACT_APP_IS_STORE_VERSION=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "generate-version": "node scripts/generate-version.js", "start:landing": "cd landing && npm start", "start:test_app": "react-scripts start", "build:landing": "cd landing && npm run build", "build:test_app": "react-scripts build", "build:all": "npm run build && npm run build:landing", "deploy": "npm run build:all && firebase deploy", "deploy:app": "npm run build && firebase deploy --only hosting:app", "deploy:landing": "npm run build:landing && firebase deploy --only hosting:landing", "deploy:test_app": "npm run build:test_app && firebase deploy --only hosting:test_app", "deploy:functions": "firebase deploy --only functions", "lint": "eslint --ext .js,.jsx,.ts,.tsx src/", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx src/ --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.3", "sharp": "^0.34.3"}}